"""
Routes for Mentor-Institute Collaborations Management
Handles ONLY collaboration CRUD operations (not invitations)
For invitations, use the MentorInstitute router
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Mentors.CollaborationCrud import (
    create_collaboration, get_collaboration_by_id, update_collaboration,
    delete_collaboration, list_collaborations
)

# Import Schemas
from Schemas.Mentors.MentorInstitutes import (
    CollaborationCreate, CollaborationUpdate, CollaborationDetails,
    CollaborationListResponse
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# === COLLABORATION MANAGEMENT ROUTES ===


@router.get("/{collaboration_id}", response_model=CollaborationDetails)
def get_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get collaboration details by ID"""
    current_user = get_current_user(token, db)
    collaboration = get_collaboration_by_id(db, collaboration_id)
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    # Check if user has access to this collaboration
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return collaboration


@router.put("/{collaboration_id}", response_model=CollaborationDetails)
def update_collaboration_details(
    collaboration_id: UUID,
    update_data: CollaborationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Update collaboration details"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return update_collaboration(db, collaboration_id, update_data)


@router.delete("/{collaboration_id}")
def delete_collaboration_endpoint(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Delete/end collaboration"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    delete_collaboration(db, collaboration_id)
    return {"message": "Collaboration deleted successfully"}


@router.get("/", response_model=CollaborationListResponse)
def list_user_collaborations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """List collaborations for current user"""
    current_user = get_current_user(token, db)
    
    return list_collaborations(
        db=db,
        user_id=current_user.id,
        user_type=current_user.user_type,
        status_filter=status_filter,
        page=page,
        size=size
    )


