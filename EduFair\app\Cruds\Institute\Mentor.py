from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
import bcrypt
import json

# Import Models
from Models.users import User, UserTypeEnum, MentorProfile, MentorInstituteAssociation, MentorInstituteInvite as MentorInstituteInviteModel, InstituteProfile, Subject
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Mentors.Mentor import (
    MentorProfileUpdate,
    MentorUserOut, MentorDetailedOut, MentorProfileOut, MentorListOut, MentorListResponse
)
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite as MentorInstituteInviteSchema, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationDetails, CollaborationCreate, CollaborationUpdate, InvitationSenderDetails
)
from Schemas.Institute.Institute import InstituteListOut

# Import Utilities
from utils.image_utils import get_profile_image_data


def _populate_sender_details(db: Session, invite: MentorInstituteInviteModel) -> InvitationSenderDetails:
    """Helper function to populate sender details for invitations"""

    # Determine who is the sender based on received_by field
    if invite.received_by == "mentor":
        # Institute sent the invite to mentor
        sender_user = db.query(User).options(
            joinedload(User.institute_profile)
        ).filter(User.id == invite.institute_id).first()

        if not sender_user:
            return None

        sender_details = InvitationSenderDetails(
            id=sender_user.id,
            username=sender_user.username,
            email=sender_user.email,
            profile_picture=sender_user.profile_picture
        )

        # Add institute-specific details
        if sender_user.institute_profile:
            profile = sender_user.institute_profile
            sender_details.institute_name = profile.institute_name
            sender_details.institute_description = profile.description
            sender_details.institute_website = profile.website
            sender_details.institute_city = profile.city
            sender_details.institute_country = sender_user.country
            sender_details.institute_is_verified = profile.is_verified

            # Get institute logo image data
            if profile.logo_url:
                sender_details.institute_logo = get_profile_image_data(profile.logo_url)

    elif invite.received_by == "institute":
        # Mentor sent the invite to institute
        sender_user = db.query(User).options(
            joinedload(User.mentor_profile)
        ).filter(User.id == invite.mentor_id).first()

        if not sender_user:
            return None

        sender_details = InvitationSenderDetails(
            id=sender_user.id,
            username=sender_user.username,
            email=sender_user.email,
            profile_picture=sender_user.profile_picture
        )

        # Add mentor-specific details
        if sender_user.mentor_profile:
            profile = sender_user.mentor_profile
            sender_details.mentor_bio = profile.bio
            sender_details.mentor_experience_years = profile.experience_years
            sender_details.mentor_hourly_rate = float(profile.hourly_rate) if profile.hourly_rate else None
            sender_details.mentor_languages = profile.languages

            # Get mentor profile image data
            if profile.profile_image_url:
                sender_details.profile_image = get_profile_image_data(profile.profile_image_url)

    else:
        return None

    # Get profile image data
    if sender_user.profile_picture:
        sender_details.profile_image = get_profile_image_data(sender_user.profile_picture)

    return sender_details




def get_mentor_with_profile_by_id(db: Session, mentor_id: uuid.UUID) -> MentorDetailedOut:
    """Get mentor by ID with only profile details (no stats)."""

    # Fetch mentor user + profile
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Ensure mentor profile exists
    if not user.mentor_profile:
        mentor_profile = MentorProfile(user_id=user.id)
        db.add(mentor_profile)
        db.commit()
        db.refresh(mentor_profile)
        user.mentor_profile = mentor_profile

    profile = user.mentor_profile

    # Parse JSON fields
    languages = json.loads(profile.languages) if profile.languages else []
    availability_hours = json.loads(profile.availability_hours) if profile.availability_hours else {}

    # Image handling
    primary_image_url = user.profile_picture or profile.profile_image_url
    profile_image_data = get_profile_image_data(primary_image_url, None)

    # Mentor profile schema
    profile_out = MentorProfileOut(
        id=profile.id,
        user_id=profile.user_id,
        bio=profile.bio,
        experience_years=profile.experience_years,
        languages=languages,
        hourly_rate=profile.hourly_rate,
        availability_hours=availability_hours,
        profile_image_url=primary_image_url,
        profile_image=profile_image_data,
        expertise_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "expertise_subjects", []) if s
        ],
        preferred_subjects=[
            {"id": str(s.id), "name": s.name} for s in getattr(profile, "preferred_subjects", []) if s
        ],
        created_at=profile.created_at,
        updated_at=profile.updated_at,
    )

    # Mentor user schema
    user_out = MentorUserOut(
        id=user.id,
        username=user.username,
        email=user.email,
        mobile=user.mobile,
        country=user.country,
        profile_picture=user.profile_picture,
        profile_image=profile_image_data,
        user_type=str(user.user_type.value),
        is_email_verified=user.is_email_verified,
        is_mobile_verified=user.is_mobile_verified,
        created_at=user.created_at,
        mentor_profile=profile_out
    )

    # Final response — ONLY profile data, no stats
    return MentorDetailedOut(
        user=user_out,
        profile=profile_out,
        total_competitions=0,
        active_institutes=0,
        average_rating=None,
        verification_status="pending"
    )



def get_mentors(
    db: Session,
    page: int = 1,
    size: int = 20,
    search: Optional[str] = None,
    subject_filter: Optional[str] = None,
    min_experience: Optional[int] = None,
    max_hourly_rate: Optional[float] = None
) -> MentorListResponse:
    """Get mentors with filtering and pagination"""
    # Calculate skip for pagination
    skip = (page - 1) * size

    query = db.query(User).options(
        joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
        joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
    ).join(MentorProfile, MentorProfile.user_id == User.id).filter(User.user_type == UserTypeEnum.mentor)

    # Add search functionality
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                MentorProfile.bio.ilike(search_term)
            )
        )

    # Filter by subject expertise
    if subject_filter:
        query = query.join(MentorProfile.expertise_subjects).filter(
            Subject.name.ilike(f"%{subject_filter}%")
        )

    # Filter by minimum experience
    if min_experience is not None:
        query = query.filter(MentorProfile.experience_years >= min_experience)

    # Filter by maximum hourly rate
    if max_hourly_rate is not None:
        query = query.filter(MentorProfile.hourly_rate <= max_hourly_rate)

    # Get total count for pagination
    total = query.count()

    # Get mentors with pagination
    mentors = query.offset(skip).limit(size).all()

    # Convert to MentorListOut objects
    mentor_list = []
    for user in mentors:
        mentor_profile = user.mentor_profile

        # Get profile image data - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)
        primary_image_url = user_profile_picture or mentor_profile_image_url

        # Get profile image data as base64
        profile_image_data = get_profile_image_data(primary_image_url, None) if primary_image_url else None

        # Get expertise areas from subjects
        expertise_areas = []
        if mentor_profile and hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            expertise_areas = [subject.name for subject in mentor_profile.expertise_subjects if subject]

        # Parse languages from JSON
        languages = []
        if mentor_profile and mentor_profile.languages:
            try:
                languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
            except (json.JSONDecodeError, TypeError):
                languages = []

        # Create full name from username (could be enhanced with first_name/last_name if available)
        full_name = user.username

        mentor_list.append(MentorListOut(
            id=user.id,
            username=user.username,
            full_name=full_name,
            email=user.email,
            mobile=user.mobile,
            country=user.country,
            bio=mentor_profile.bio if mentor_profile else None,
            expertise_areas=expertise_areas,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            current_position=None,  # Not available in current schema
            hourly_rate=mentor_profile.hourly_rate if mentor_profile else None,
            languages=languages,
            rating=None,  # Not implemented yet
            is_verified=user.is_email_verified,
            verification_status="pending",  # Default status
            profile_image_url=primary_image_url,
            profile_image=profile_image_data,
            created_at=user.created_at
        ))

    # Calculate pagination info
    has_next = (skip + size) < total
    has_prev = page > 1

    return MentorListResponse(
        mentors=mentor_list,
        total=total,
        page=page,
        size=size,
        has_next=has_next,
        has_prev=has_prev
    )
   
def update_mentor_profile(
    db: Session,
    mentor_id: uuid.UUID,
    profile_update: MentorProfileUpdate
) -> MentorDetailedOut:
    """Update mentor profile"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle subject relationships separately
    expertise_subject_ids = update_data.pop('expertise_subject_ids', None)
    preferred_subject_ids = update_data.pop('preferred_subject_ids', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            # Handle JSON fields
            if field in ['languages'] and value:
                setattr(profile, field, json.dumps(value))
            elif field == 'availability_hours' and value:
                setattr(profile, field, json.dumps(value))
            else:
                setattr(profile, field, value)

    # Update expertise subjects
    if expertise_subject_ids is not None:
        from Models.users import Subject
        profile.expertise_subjects.clear()
        if expertise_subject_ids:
            expertise_subjects = db.query(Subject).filter(Subject.id.in_(expertise_subject_ids)).all()
            profile.expertise_subjects.extend(expertise_subjects)

    # Update preferred subjects
    if preferred_subject_ids is not None:
        from Models.users import Subject
        profile.preferred_subjects.clear()
        if preferred_subject_ids:
            preferred_subjects = db.query(Subject).filter(Subject.id.in_(preferred_subject_ids)).all()
            profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return get_mentor_with_profile_by_id(db, mentor_id)


def create_invite_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Mentor sends invite to institute"""

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == mentor_id,
        MentorInstituteInviteModel.institute_id == invite_data.receiver_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=mentor_id,
        institute_id=invite_data.receiver_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (institute in this case)
        hourly_rate=invite_data.hourly_rate,
        hours_per_week=invite_data.hours_per_week,
        received_by="institute"  # Institute receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)


def create_invite_to_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invite_data: MentorInstituteInviteSchema
) -> MentorInstituteInviteOut:
    """Institute sends invite to mentor"""

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Verify mentor exists
    mentor = db.query(User).filter(
        User.id == invite_data.receiver_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == invite_data.receiver_id,
        MentorInstituteInviteModel.institute_id == institute_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()

    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invite already exists")

    # Create invite
    invite = MentorInstituteInviteModel(
        mentor_id=invite_data.receiver_id,
        institute_id=institute_id,
        receiver_id=invite_data.receiver_id,  # Who receives the invite (mentor in this case)
        hourly_rate=invite_data.hourly_rate,
        hours_per_week=invite_data.hours_per_week,
        received_by="mentor"  # Mentor receives this invite
    )
    db.add(invite)
    db.commit()
    db.refresh(invite)

    return MentorInstituteInviteOut.model_validate(invite)

def list_sent_invites_to_institutes(
    db: Session,
    mentor_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invites sent by mentor to institutes"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.mentor_id == mentor_id,
        MentorInstituteInviteModel.received_by == "institute"
    )

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()

    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)
        invite_out.sender = _populate_sender_details(db, invite)
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def delete_invite_sent_to_institute(
    db: Session,
    invite_id: uuid.UUID
) -> bool:
    """Mentor deletes invite sent to institute"""
    
    invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.id == invite_id,
        MentorInstituteInviteModel.status == "pending"
    ).first()
    
    if not invite:
        raise HTTPException(status_code=404, detail="Invite not found or already processed")
    
    db.delete(invite)
    db.commit()
    
    return True

def list_received_invites_from_institutes(
    db: Session,
    user_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invites received by user (mentor or institute)"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.receiver_id == user_id
    )

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    total = query.count()
    invites = query.order_by(desc(MentorInstituteInviteModel.invited_at)).offset(skip).limit(size).all()

    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)
        invite_out.sender = _populate_sender_details(db, invite)
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )



def respond_to_received_invite(
    db: Session,
    user_id: uuid.UUID,
    invite_id: uuid.UUID,
    response: dict
) -> CollaborationDetails:
    """User (mentor or institute) responds to an invite they received"""

    # Get the invite - check both mentor and institute scenarios
    invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.id == invite_id,
        or_(
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == "mentor"
            ),
            and_(
                MentorInstituteInviteModel.receiver_id == user_id,
                MentorInstituteInviteModel.received_by == "institute"
            )
        )
    ).first()

    if not invite:
        raise HTTPException(status_code=404, detail="Invite not found")

    if invite.status != "pending":
        raise HTTPException(status_code=400, detail="Invite already responded to")

    # Update invite status based on response
    action = response.get("action", "").lower()

    if action == "accept":
        invite.status = "accepted"
        invite.responded_at = datetime.now(timezone.utc)

        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=invite.mentor_id,
            institute_id=invite.institute_id,
            status="active",
            hourly_rate=response.get("hourly_rate") or invite.proposed_hourly_rate or invite.hourly_rate,
            hours_per_week=response.get("hours_per_week") or invite.proposed_hours_per_week or invite.hours_per_week,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invite.id
        )

        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)

        # Import the helper function from our new CRUD
        from Cruds.Mentors.CollaborationCrud import _format_collaboration_details
        return _format_collaboration_details(db, collaboration)

    elif action == "reject":
        invite.status = "declined"
        invite.responded_at = datetime.now(timezone.utc)
        invite.response_message = response.get("message", "Invitation declined")
        db.commit()
        return None  # No collaboration created for rejection

    else:
        raise HTTPException(status_code=400, detail="Invalid action. Use 'accept' or 'reject'")


def list_sent_invites_to_mentors(
    db: Session,
    institute_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """List invites sent by institute to mentors"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.institute_id == institute_id,
        MentorInstituteInviteModel.received_by == "mentor"
    )

    if status_filter:
        query = query.filter(MentorInstituteInviteModel.status == status_filter)

    # Get total count
    total = query.count()

    # Get paginated results
    invites = query.order_by(desc(MentorInstituteInviteModel.created_at)).offset(skip).limit(size).all()

    # Convert to response format with sender details
    invite_details = []
    for invite in invites:
        invite_out = MentorInstituteInviteOut.model_validate(invite)
        invite_out.sender = _populate_sender_details(db, invite)
        invite_details.append(invite_out)

    return InvitationListResponse(
        invitations=invite_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def respond_to_received_invite_as_institute(
    db: Session,
    institute_id: uuid.UUID,
    invite_id: uuid.UUID,
    response: dict
) -> CollaborationDetails:
    """Institute responds to an invite received from mentor"""

    # Get the invite
    invite = db.query(MentorInstituteInviteModel).filter(
        MentorInstituteInviteModel.id == invite_id,
        MentorInstituteInviteModel.institute_id == institute_id,
        MentorInstituteInviteModel.received_by == "institute"
    ).first()

    if not invite:
        raise HTTPException(status_code=404, detail="Invite not found")

    if invite.status != "pending":
        raise HTTPException(status_code=400, detail="Invite already responded to")

    # Update invite status
    if response.get("accept", False):
        invite.status = "accepted"

        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=invite.mentor_id,
            institute_id=institute_id,
            status="active",
            hourly_rate=response.get("hourly_rate") or invite.hourly_rate,
            hours_per_week=response.get("hours_per_week") or invite.hours_per_week,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invite.id
        )

        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)

        # Import the helper function from our new CRUD
        from Cruds.Mentors.CollaborationCrud import _format_collaboration_details
        return _format_collaboration_details(db, collaboration)
    else:
        invite.status = "declined"
        db.commit()
        raise HTTPException(status_code=400, detail="Invite declined")