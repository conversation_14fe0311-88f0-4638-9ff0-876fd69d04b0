"""
In-Memory Caching Utilities for Performance Optimization
"""

import json
import hashlib
import time
from typing import Any, Optional, Callable, Union, Dict
from functools import wraps
from datetime import datetime, timedelta
from threading import Lock


# Global in-memory cache storage
_cache_storage: Dict[str, Dict[str, Any]] = {}
_cache_lock = Lock()


class CacheManager:
    """Centralized in-memory cache management for EduFair"""

    def __init__(self):
        self.storage = _cache_storage
        self.lock = _cache_lock

    def _is_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """Check if cache entry is expired"""
        if 'expires_at' not in cache_entry:
            return False
        return datetime.now().timestamp() > cache_entry['expires_at']

    def _cleanup_expired(self):
        """Remove expired entries from cache"""
        current_time = datetime.now().timestamp()
        expired_keys = [
            key for key, entry in self.storage.items()
            if 'expires_at' in entry and current_time > entry['expires_at']
        ]
        for key in expired_keys:
            del self.storage[key]

    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            with self.lock:
                self._cleanup_expired()
                if key in self.storage:
                    entry = self.storage[key]
                    if not self._is_expired(entry):
                        return entry['value']
                    else:
                        del self.storage[key]
                return None
        except Exception as e:
            print(f"Cache get error for key {key}: {e}")
            return None

    async def set(self, key: str, value: Any, expire: int = 3600) -> bool:
        """Set value in cache with expiration"""
        try:
            with self.lock:
                expires_at = datetime.now().timestamp() + expire
                self.storage[key] = {
                    'value': value,
                    'expires_at': expires_at,
                    'created_at': datetime.now().timestamp()
                }
                return True
        except Exception as e:
            print(f"Cache set error for key {key}: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            with self.lock:
                if key in self.storage:
                    del self.storage[key]
                return True
        except Exception as e:
            print(f"Cache delete error for key {key}: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            with self.lock:
                self._cleanup_expired()
                return key in self.storage and not self._is_expired(self.storage[key])
        except Exception as e:
            print(f"Cache exists error for key {key}: {e}")
            return False

    def generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        # Create a unique key based on function arguments
        key_data = f"{prefix}:{args}:{sorted(kwargs.items())}"
        key_hash = hashlib.md5(key_data.encode()).hexdigest()
        return f"cache:{prefix}:{key_hash}"

    def clear_all(self) -> bool:
        """Clear all cache entries"""
        try:
            with self.lock:
                self.storage.clear()
                return True
        except Exception as e:
            print(f"Cache clear error: {e}")
            return False


# Cache decorators for different use cases
def cache_result(expire: int = 3600, key_prefix: str = None):
    """
    Decorator to cache function results in memory

    Args:
        expire: Cache expiration time in seconds (default: 1 hour)
        key_prefix: Custom prefix for cache key (default: function name)
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get cache manager
            cache_manager = CacheManager()

            # Generate cache key
            prefix = key_prefix or func.__name__
            cache_key = cache_manager.generate_key(prefix, *args, **kwargs)

            # Try to get from cache first
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function if not in cache
            if hasattr(func, '__await__'):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            # Store result in cache
            await cache_manager.set(cache_key, result, expire)

            return result
        return wrapper
    return decorator


# Specific cache configurations for different data types
class CacheConfig:
    """Cache configuration constants"""
    
    # Student statistics caching (30 minutes)
    STUDENT_STATS_EXPIRE = 1800
    
    # Rankings caching (15 minutes - more frequent updates)
    RANKINGS_EXPIRE = 900
    
    # Subject/Chapter lists (2 hours - rarely change)
    METADATA_EXPIRE = 7200
    
    # AI checking results (permanent - never changes once set)
    AI_RESULTS_EXPIRE = 86400 * 7  # 1 week
    
    # User profiles (1 hour)
    USER_PROFILE_EXPIRE = 3600
    
    # Exam data (30 minutes)
    EXAM_DATA_EXPIRE = 1800


# Specific caching functions for common operations
async def cache_student_statistics(student_id: str, statistics_data: dict):
    """Cache student statistics with appropriate expiration"""
    cache_manager = CacheManager()
    key = f"student_stats:{student_id}"
    await cache_manager.set(key, statistics_data, CacheConfig.STUDENT_STATS_EXPIRE)


async def get_cached_student_statistics(student_id: str) -> Optional[dict]:
    """Get cached student statistics"""
    cache_manager = CacheManager()
    key = f"student_stats:{student_id}"
    return await cache_manager.get(key)


async def cache_student_ranking(student_id: str, ranking_data: dict):
    """Cache student ranking with appropriate expiration"""
    cache_manager = CacheManager()
    key = f"student_ranking:{student_id}"
    await cache_manager.set(key, ranking_data, CacheConfig.RANKINGS_EXPIRE)


async def get_cached_student_ranking(student_id: str) -> Optional[dict]:
    """Get cached student ranking"""
    cache_manager = CacheManager()
    key = f"student_ranking:{student_id}"
    return await cache_manager.get(key)


async def cache_subject_list(subjects_data: list):
    """Cache subject list (rarely changes)"""
    cache_manager = CacheManager()
    key = "subjects_list"
    await cache_manager.set(key, subjects_data, CacheConfig.METADATA_EXPIRE)


async def get_cached_subject_list() -> Optional[list]:
    """Get cached subject list"""
    cache_manager = CacheManager()
    key = "subjects_list"
    return await cache_manager.get(key)


async def cache_ai_checking_result(exam_id: str, student_id: str, result_data: dict):
    """Cache AI checking results (permanent)"""
    cache_manager = CacheManager()
    key = f"ai_result:{exam_id}:{student_id}"
    await cache_manager.set(key, result_data, CacheConfig.AI_RESULTS_EXPIRE)


async def get_cached_ai_checking_result(exam_id: str, student_id: str) -> Optional[dict]:
    """Get cached AI checking results"""
    cache_manager = CacheManager()
    key = f"ai_result:{exam_id}:{student_id}"
    return await cache_manager.get(key)


async def invalidate_student_cache(student_id: str):
    """Invalidate all cache entries for a student (when data changes)"""
    cache_manager = CacheManager()

    # List of cache keys to invalidate
    keys_to_delete = [
        f"student_stats:{student_id}",
        f"student_ranking:{student_id}",
    ]

    # For AI results, we need to find all keys that match the pattern
    with cache_manager.lock:
        ai_result_keys = [
            key for key in cache_manager.storage.keys()
            if key.startswith(f"ai_result:") and key.endswith(f":{student_id}")
        ]
        keys_to_delete.extend(ai_result_keys)

    for key in keys_to_delete:
        await cache_manager.delete(key)


async def invalidate_rankings_cache():
    """Invalidate all ranking caches (when new exam results are added)"""
    cache_manager = CacheManager()

    # Find all ranking cache keys and delete them
    with cache_manager.lock:
        ranking_keys = [
            key for key in cache_manager.storage.keys()
            if key.startswith("student_ranking:")
        ]

    for key in ranking_keys:
        await cache_manager.delete(key)


# Cache warming functions (pre-populate cache with frequently accessed data)
async def warm_cache_for_active_students(db_session):
    """Pre-populate cache for active students"""
    # This would be called during off-peak hours or after major data updates
    # Implementation would fetch and cache data for recently active students
    cache_manager = CacheManager()

    # Example implementation (to be customized based on needs):
    # 1. Query recently active students from database
    # 2. Pre-calculate and cache their statistics
    # 3. Pre-calculate and cache their rankings
    pass


# Cache monitoring and statistics
async def get_cache_stats() -> dict:
    """Get cache performance statistics"""
    try:
        cache_manager = CacheManager()
        with cache_manager.lock:
            total_entries = len(cache_manager.storage)
            expired_entries = sum(
                1 for entry in cache_manager.storage.values()
                if cache_manager._is_expired(entry)
            )
            active_entries = total_entries - expired_entries

        return {
            'total_entries': total_entries,
            'active_entries': active_entries,
            'expired_entries': expired_entries,
            'cache_type': 'in-memory'
        }
    except Exception as e:
        return {'error': str(e)}


# Usage examples:
"""
# Using the decorator
@cache_result(expire=1800, key_prefix="student_performance")
async def get_student_performance(student_id: str):
    # Expensive database operation
    return calculate_performance(student_id)

# Manual caching
await cache_student_statistics(student_id, stats_data)
cached_stats = await get_cached_student_statistics(student_id)
"""
