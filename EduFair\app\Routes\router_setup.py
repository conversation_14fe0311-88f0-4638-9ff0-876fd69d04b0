"""
Router setup module for organizing all API routes.

This module centralizes the configuration of all API routers and their prefixes.
"""

from fastapi import FastAPI

# Import all routers
from Routes.users import router as user_router
from Routes.health import router as health_router
from Routes.TeacherModule.Classroom import router as classroom_router
from Routes.Exams.Subjects import router as subject_router
from Routes.Exams.Chapters import router as chapter_router
from Routes.Exams.Topics import router as topic_router
from Routes.Exams.Subtopics import router as subtopic_router
from Routes.TeacherModule.Tasks import router as task_router
from Routes.TeacherModule.TeacherProfile import router as teacher_profile_router
from Routes.Exams.Plan import router as plan_router
from Routes.TeacherModule.announcement import router as announcement_router
from Routes.TeacherModule.Class import router as class_router
from Routes.Exams.Questions import router as question_router
from Routes.Exams.Exam import router as exam_router
from Routes.Exams.ExamSession import router as exam_session_router
from Routes.Exams.ExamChecking import router as exam_checking_router
from Routes.StudentDashboard import router as student_dashboard_router
from Routes.StudentStatistics import router as student_statistics_router
from Routes.file_upload import router as file_upload_router
from Routes.file_admin import router as file_admin_router
from Routes.Events.Events import router as events_router
from Routes.Events.Payments import router as payments_router
from Routes.Events.Calendar import router as calendar_router
from Routes.Events.Competitions import router as competitions_router
from Routes.Events.CompetitionQuestions import router as competition_questions_router
from Routes.Events.CompetitionSecurity import router as competition_security_router
from Routes.Events.MentorChecking import router as mentor_checking_router
from Routes.Events.CompetitionAnalytics import router as competition_analytics_router
from Routes.Institute.Institute import router as institute_router
from Routes.Institute.Mentor import router as mentor_router
from Routes.Institute.Dashboard import router as institute_dashboard_router
from Routes.Institute.EventManagement import router as institute_event_management_router
from Routes.Institute.MentorInstitute import router as mentor_institutes_router
from Routes.Subscriptions import router as subscriptions_router
from Routes.Auth.email_verification import router as email_verification_router
from Routes.Mentors.Collaborations import router as mentor_collaborations_router
# Import admin routes
from Routes.admin_routes import admin_institute_router


def setup_routers(app: FastAPI):
    """
    Configure all API routers for the FastAPI application.
    
    Args:
        app (FastAPI): The FastAPI application instance
    """
    # Health check router (no authentication required)
    app.include_router(health_router, prefix="/api/health", tags=["health"])
    
    # Authentication and user management
    app.include_router(user_router, prefix="/api/users", tags=["users"])
    app.include_router(email_verification_router, prefix="/api", tags=["email-verification"])
    
    # Teacher module routes
    app.include_router(classroom_router, prefix="/api/classrooms", tags=["classrooms"])
    app.include_router(task_router, prefix="/api/tasks", tags=["tasks"])
    app.include_router(teacher_profile_router, prefix="/api/teachers", tags=["teacherProfiles"])
    app.include_router(announcement_router, prefix="/api/announcements", tags=["announcements"])
    app.include_router(class_router, prefix="/api/classes", tags=["classes"])
    
    # Exam system routes
    app.include_router(subject_router, prefix="/api/subjects", tags=["subjects"])
    app.include_router(chapter_router, prefix="/api/chapters", tags=["chapters"])
    app.include_router(topic_router, prefix="/api/topics", tags=["topics"])
    app.include_router(subtopic_router, prefix="/api/subtopics", tags=["subtopics"])
    app.include_router(plan_router, prefix="/api/plans", tags=["plans"])
    app.include_router(question_router, prefix="/api/questions", tags=["questions"])
    app.include_router(exam_router, prefix="/api/exams", tags=["exams"])
    app.include_router(exam_session_router, prefix="/api/exams/session", tags=["examSession"])
    app.include_router(exam_session_router, tags=["exam-websocket"])  # No prefix for WebSocket routes
    app.include_router(exam_checking_router, prefix="/api/exams/checking", tags=["examChecking"])
    
    # Student routes
    app.include_router(student_dashboard_router, prefix="/api/student", tags=["studentDashboard"])
    app.include_router(student_statistics_router, prefix="/api/student", tags=["studentStatistics"])
    
    # File management
    app.include_router(file_upload_router, prefix="/api/files", tags=["fileUpload"])
    app.include_router(file_admin_router, prefix="/api/admin/files", tags=["fileAdmin"])
    
    # Events and competitions
    app.include_router(events_router, prefix="/api/events", tags=["events"])
    app.include_router(payments_router, prefix="/api/payments", tags=["payments"])
    app.include_router(calendar_router, prefix="/api", tags=["calendar"])
    app.include_router(competitions_router, prefix="/api", tags=["competitions"])
    app.include_router(competition_questions_router, prefix="/api", tags=["competition-questions"])
    app.include_router(competition_security_router, prefix="/api", tags=["competition-security"])
    app.include_router(mentor_checking_router, prefix="/api", tags=["mentor-checking"])
    app.include_router(competition_analytics_router, prefix="/api", tags=["competition-analytics"])
    
    # Institute management
    app.include_router(institute_router, prefix="/api/institutes", tags=["institutes"])
    app.include_router(mentor_router, prefix="/api/mentors", tags=["mentors"])
    app.include_router(mentor_institutes_router, prefix="/api/institute/mentors", tags=["institute-mentors"])
    app.include_router(mentor_collaborations_router, prefix="/api/mentor/collaborations", tags=["mentor-collaborations"])
    app.include_router(institute_dashboard_router, prefix="/api/institute/dashboard", tags=["institute-dashboard"])
    app.include_router(institute_event_management_router, prefix="/api/institute/events", tags=["institute-events"])
    
    # Subscriptions
    app.include_router(subscriptions_router, prefix="/api/subscriptions", tags=["subscriptions"])
    
    # Admin routes
    app.include_router(admin_institute_router, prefix="/api/admin", tags=["admin-institutes"])
