from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Make sure to URL-encode special characters in the password!
password = "qDl#XjWW4M]69O8"

#SQLALCHEMY_DATABASE_URL = f"postgresql://postgres:{password}@*************:5432/edufair"
SQLALCHEMY_DATABASE_URL = f"postgresql://postgres:1234@localhost:5432/edufair"
# Create engine with connection pool settings
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # Set to True for SQL debugging
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()
